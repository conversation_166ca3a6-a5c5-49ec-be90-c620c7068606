{"log": {"access": "", "error": "", "loglevel": "warning"}, "inbounds": [{"tag": "socks", "port": 10808, "listen": "0.0.0.0", "protocol": "socks", "sniffing": {"enabled": true, "destOverride": ["http", "tls"], "routeOnly": false}, "settings": {"auth": "<PERSON><PERSON><PERSON>", "udp": true, "allowTransparent": false}}, {"tag": "http", "port": 10809, "listen": "0.0.0.0", "protocol": "http", "sniffing": {"enabled": true, "destOverride": ["http", "tls"], "routeOnly": false}, "settings": {"auth": "<PERSON><PERSON><PERSON>", "udp": true, "allowTransparent": false}}], "outbounds": [], "dns": {"servers": ["1.1.1.1", "8.8.8.8"]}, "routing": {"domainStrategy": "AsIs", "rules": []}}