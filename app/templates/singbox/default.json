{"log": {"level": "warn"}, "dns": {"servers": [{"tag": "dns-remote", "address": "*******", "detour": "proxy"}, {"tag": "dns-local", "address": "local", "detour": "direct"}], "rules": [{"outbound": "any", "server": "dns-local"}], "final": "dns-remote", "strategy": "prefer_ipv4"}, "inbounds": [{"type": "tun", "tag": "tun-in", "interface_name": "sing-tun", "address": ["**********/30", "fdfe:dcba:9876::1/126"], "auto_route": true, "route_exclude_address": ["***********/16", "fc00::/7"], "sniff": true, "domain_strategy": "prefer_ipv4"}], "outbounds": [{"type": "selector", "tag": "proxy", "outbounds": null, "interrupt_exist_connections": true}, {"type": "urltest", "tag": "Best Latency", "outbounds": null}, {"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}, {"type": "dns", "tag": "dns-out"}], "route": {"rules": [{"protocol": "dns", "outbound": "dns-out"}, {"ip_is_private": true, "outbound": "direct"}], "final": "proxy", "auto_detect_interface": true, "override_android_vpn": true}, "experimental": {"cache_file": {"enabled": true, "store_rdrc": true}}}