"""add created_at field to users table

Revision ID: 9b60be6cd0a2
Revises: 9d5a518ae432
Create Date: 2022-12-25 18:45:46.743506

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9b60be6cd0a2'
down_revision = '9d5a518ae432'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users') as batch_op:
        batch_op.add_column(sa.Column(
            'created_at', sa.DateTime(),
            nullable=False,
            server_default=sa.func.current_timestamp()))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users') as batch_op:
        batch_op.drop_column('created_at')
    # ### end Alembic commands ###
