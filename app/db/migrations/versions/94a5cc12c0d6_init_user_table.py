"""init user table

Revision ID: 94a5cc12c0d6
Revises: 
Create Date: 2022-11-18 20:54:05.616546

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '94a5cc12c0d6'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('users',
                    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
                    sa.<PERSON>umn('username', sa.String(34), nullable=True),
                    sa.<PERSON>umn('proxy_type', sa.<PERSON>um('VMess', 'VLESS', 'Trojan', 'Shadowsocks', name='proxytypes'), nullable=False),
                    sa.Column('settings', sa.JSON(), nullable=False),
                    sa.Column('status', sa.Enum('active', 'limited', 'expired', name='userstatus'), nullable=True),
                    sa.Column('used_traffic', sa.<PERSON>Integer(), nullable=True),
                    sa.Column('data_limit', sa.BigInteger(), nullable=True),
                    sa.<PERSON>umn('expire', sa.Integer(), nullable=True),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_table('users')
    # ### end Alembic commands ###
