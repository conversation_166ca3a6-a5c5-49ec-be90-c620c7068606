"""user_last_status_change

Revision ID: 07f9bbb3db4e
Revises: ccbf9d322ae3
Create Date: 2024-04-23 09:57:24.697613

"""

from alembic import op
import sqlalchemy as sa
from datetime import datetime

# revision identifiers, used by Alembic.
revision = '07f9bbb3db4e'
down_revision = 'ccbf9d322ae3'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Add the column 'last_status_change'
    op.add_column('users', sa.Column('last_status_change', sa.DateTime(), nullable=True))

    # Define the 'users' table for SQLAlchemy Core operations
    users_table = sa.Table(
        'users',
        sa.MetaData(),
        sa.Column('id', sa.Integer, primary_key=True),
        sa.Column('last_status_change', sa.DateTime())
    )

    # Set last_status_change to the current UTC time for rows where it's NULL
    connection = op.get_bind()
    update_stmt = (
        sa.update(users_table)
        .where(users_table.c.last_status_change.is_(None))
        .values(last_status_change=datetime.utcnow())
    )
    connection.execute(update_stmt)

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop the 'last_status_change' column
    op.drop_column('users', 'last_status_change')
    # ### end Alembic commands ###
