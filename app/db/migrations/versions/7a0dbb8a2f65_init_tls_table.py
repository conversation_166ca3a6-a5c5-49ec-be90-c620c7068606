"""init tls table

Revision ID: 7a0dbb8a2f65
Revises: 77c86a261126
Create Date: 2023-10-22 13:58:12.431246

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
from app.utils.crypto import generate_certificate

# revision identifiers, used by Alembic.
revision = '7a0dbb8a2f65'
down_revision = '77c86a261126'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    table = op.create_table('tls',
                            sa.Column('id', sa.Integer(), nullable=False),
                            sa.Column('key', sa.String(length=4096), nullable=False),
                            sa.Column('certificate', sa.String(length=2048), nullable=False),
                            sa.PrimaryKeyConstraint('id')
                            )

    # INSERT DEFAULT ROW
    tls = generate_certificate()
    op.bulk_insert(table, [{"id": 1, "key": tls['key'], "certificate": tls['cert']}])

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('tls')
    # ### end Alembic commands ###
