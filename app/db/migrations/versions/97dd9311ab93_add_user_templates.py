"""add user templates

Revision ID: 97dd9311ab93
Revises: e410e5f15c3f
Create Date: 2023-03-26 04:09:42.829125

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '97dd9311ab93'
down_revision = 'e410e5f15c3f'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_templates',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=64), nullable=False),
    sa.Column('data_limit', sa.Integer(), nullable=True),
    sa.Column('expire_duration', sa.Integer(), nullable=True),
    sa.Column('username_prefix', sa.String(length=20), nullable=True),
    sa.Column('username_suffix', sa.String(length=20), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_user_templates_id'), 'user_templates', ['id'], unique=False)
    op.create_table('template_inbounds_association',
    sa.Column('user_template_id', sa.Integer(), nullable=True),
    sa.Column('inbound_tag', sa.String(length=256), nullable=True),
    sa.ForeignKeyConstraint(['inbound_tag'], ['inbounds.tag'], ),
    sa.ForeignKeyConstraint(['user_template_id'], ['user_templates.id'], )
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('template_inbounds_association')
    op.drop_index(op.f('ix_user_templates_id'), table_name='user_templates')
    op.drop_table('user_templates')
    # ### end Alembic commands ###
