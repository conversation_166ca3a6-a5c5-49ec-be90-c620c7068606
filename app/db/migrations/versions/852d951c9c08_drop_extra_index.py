"""drop_extra_index

Revision ID: 852d951c9c08
Revises: dd725e4d3628
Create Date: 2024-02-15 23:51:53.510090

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '852d951c9c08'
down_revision = 'dd725e4d3628'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_admins_id', table_name='admins')
    op.drop_index('ix_node_usages_id', table_name='node_usages')
    op.drop_index('ix_node_user_usages_id', table_name='node_user_usages')
    op.drop_index('ix_nodes_id', table_name='nodes')
    op.drop_index('ix_notification_reminders_id', table_name='notification_reminders')
    op.drop_index('ix_proxies_id', table_name='proxies')
    op.drop_index('ix_system_id', table_name='system')
    op.drop_index('ix_user_templates_id', table_name='user_templates')
    op.drop_index('ix_user_usage_logs_id', table_name='user_usage_logs')
    op.drop_index('ix_users_id', table_name='users')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('ix_users_id', 'users', ['id'], unique=False)
    op.create_index('ix_user_usage_logs_id', 'user_usage_logs', ['id'], unique=False)
    op.create_index('ix_user_templates_id', 'user_templates', ['id'], unique=False)
    op.create_index('ix_system_id', 'system', ['id'], unique=False)
    op.create_index('ix_proxies_id', 'proxies', ['id'], unique=False)
    op.create_index('ix_notification_reminders_id', 'notification_reminders', ['id'], unique=False)
    op.create_index('ix_nodes_id', 'nodes', ['id'], unique=False)
    op.create_index('ix_node_user_usages_id', 'node_user_usages', ['id'], unique=False)
    op.create_index('ix_node_usages_id', 'node_usages', ['id'], unique=False)
    op.create_index('ix_admins_id', 'admins', ['id'], unique=False)
    # ### end Alembic commands ###
