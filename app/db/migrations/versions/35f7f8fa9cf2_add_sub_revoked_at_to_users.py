"""add sub_revoked_at to users

Revision ID: 35f7f8fa9cf2
Revises: fc01b1520e72
Create Date: 2023-07-06 15:52:00.307575

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '35f7f8fa9cf2'
down_revision = 'fc01b1520e72'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('sub_revoked_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'sub_revoked_at')
    # ### end Alembic commands ###
