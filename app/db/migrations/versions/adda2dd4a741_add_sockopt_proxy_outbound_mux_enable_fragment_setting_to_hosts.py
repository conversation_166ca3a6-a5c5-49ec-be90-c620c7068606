"""Add sockopt proxy_outbound mux_enable fragment_setting to hosts

Revision ID: adda2dd4a741
Revises: dd725e4d3628
Create Date: 2024-02-18 21:46:55.311329

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'adda2dd4a741'
down_revision = 'dd725e4d3628'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('hosts', sa.Column('sockopt', sa.JSON(), nullable=True))
    op.add_column('hosts', sa.Column('proxy_outbound', sa.JSON(), nullable=True))
    op.add_column('hosts', sa.Column('mux_enable', sa.<PERSON>(), server_default='0', nullable=False))
    op.add_column('hosts', sa.Column('fragment_setting', sa.String(length=100), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by <PERSON><PERSON>bic - please adjust! ###
    op.drop_column('hosts', 'proxy_outbound')
    op.drop_column('hosts', 'sockopt')
    op.drop_column('hosts', 'mux_enable')
    op.drop_column('hosts', 'fragment_setting')
    # ### end Alembic commands ###
