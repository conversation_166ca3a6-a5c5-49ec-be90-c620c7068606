"""node_user_usages

Revision ID: e4a86bc8ec7b
Revises: 37692c1c9715
Create Date: 2023-05-03 14:45:25.800476

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e4a86bc8ec7b'
down_revision = '37692c1c9715'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('node_user_usages',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_username', sa.String(34), nullable=True),
    sa.Column('node_id', sa.Integer(), nullable=True),
    sa.Column('used_traffic', sa.BigInteger(), nullable=True),
    sa.ForeignKeyConstraint(['node_id'], ['nodes.id'], ),
    sa.ForeignKeyConstraint(['user_username'], ['users.username'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_username', 'node_id')
    )
    op.create_index(op.f('ix_node_user_usages_id'), 'node_user_usages', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_node_user_usages_id'), table_name='node_user_usages')
    op.drop_table('node_user_usages')
    # ### end Alembic commands ###
