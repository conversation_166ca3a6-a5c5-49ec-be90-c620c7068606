"""init system table

Revision ID: 3cf36a5fde73
Revises: 94a5cc12c0d6
Create Date: 2022-11-22 04:48:55.227490

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3cf36a5fde73'
down_revision = '94a5cc12c0d6'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    table = op.create_table('system',
                            sa.Column('id', sa.Integer(), nullable=False),
                            sa.Column('uplink', sa.BigInteger(), nullable=True),
                            sa.Column('downlink', sa.BigInteger(), nullable=True),
                            sa.PrimaryKeyConstraint('id')
                            )
    op.create_index(op.f('ix_system_id'), 'system', ['id'], unique=False)

    # INSERT DEFAULT ROW
    op.bulk_insert(table, [{"id": 1, "uplink": 0, "downlink": 0}])

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_system_id'), table_name='system')
    op.drop_table('system')
    # ### end Alembic commands ###
