"""ProxyHost: security added

Revision ID: 7cbe9d91ac11
Revises: b15eba6e5867
Create Date: 2023-03-07 23:30:49.678157

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "7cbe9d91ac11"
down_revision = "e3f0e888a563"
branch_labels = None
depends_on = None


def upgrade() -> None:
    proxyhostsecurity_enum = sa.Enum('inbound_default', 'none', 'tls', name='proxyhostsecurity')
    proxyhostsecurity_enum.create(op.get_bind())

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "hosts",
        sa.Column(
            "security",
            sa.Enum("inbound_default", "none", "tls", name="proxyhostsecurity"),
            nullable=False,
            server_default="inbound_default"
        ),
    )

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("hosts", "security")
    # ### end Alembic commands ###
