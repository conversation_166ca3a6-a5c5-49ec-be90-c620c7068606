"""init jwt table

Revision ID: 9d5a518ae432
Revises: 3cf36a5fde73
Create Date: 2022-11-24 21:02:44.278773

"""
import os
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9d5a518ae432'
down_revision = '3cf36a5fde73'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    table = op.create_table('jwt',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('secret_key', sa.String(length=64), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )

    # INSERT DEFAULT ROW
    op.bulk_insert(table, [{"id": 1, "secret_key": os.urandom(32).hex()}])

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('jwt')
    # ### end Alembic commands ###
