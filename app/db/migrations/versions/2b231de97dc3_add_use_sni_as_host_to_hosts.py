"""add use sni as host to hosts

Revision ID: 2b231de97dc3
Revises: e7b869e999b4
Create Date: 2024-12-15 09:48:24.330959

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '2b231de97dc3'
down_revision = 'e7b869e999b4'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('hosts', sa.Column('use_sni_as_host', sa.<PERSON>(), server_default='0', nullable=False))
   # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('hosts', 'use_sni_as_host')
    # ### end Alembic commands ###
