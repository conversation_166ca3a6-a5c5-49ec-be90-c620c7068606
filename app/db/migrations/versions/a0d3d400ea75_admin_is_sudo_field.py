"""Admin: is_sudo field

Revision ID: a0d3d400ea75
Revises: 5b84d88804a1
Create Date: 2023-03-10 23:01:23.260630

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import expression


# revision identifiers, used by Alembic.
revision = 'a0d3d400ea75'
down_revision = '5b84d88804a1'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('admins', sa.Column('is_sudo', sa.<PERSON>(), nullable=True, server_default=expression.false()))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('admins', 'is_sudo')
    # ### end Alembic commands ###
