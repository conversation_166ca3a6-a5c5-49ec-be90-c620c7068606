"""Added users_usage to admin

Revision ID: b25e7e6be241
Revises: c3cd674b9bcd
Create Date: 2024-11-11 23:41:08.511867

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b25e7e6be241'
down_revision = 'c3cd674b9bcd'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('admins', sa.Column('users_usage', sa.BigInteger(), nullable=False, server_default="0"))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('admins', 'users_usage')
    # ### end Alembic commands ###
