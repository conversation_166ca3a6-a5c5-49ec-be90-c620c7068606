"""add online at

Revision ID: 1cf7d159fdbb
Revises: 025d427831dd
Create Date: 2023-08-23 15:48:26.281666

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1cf7d159fdbb'
down_revision = '025d427831dd'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('online_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'online_at')
    # ### end Alembic commands ###
