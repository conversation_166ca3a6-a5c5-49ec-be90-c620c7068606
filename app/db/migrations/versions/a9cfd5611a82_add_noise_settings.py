"""add noise settings

Revision ID: a9cfd5611a82
Revises: 2313cdc30da3
Create Date: 2024-09-04 18:55:55.167589

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a9cfd5611a82'
down_revision = '2313cdc30da3'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('hosts', sa.Column('noise_setting', sa.String(length=2000), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('hosts', 'noise_setting')
    # ### end Alembic commands ###
