"""add xray version

Revision ID: 57fda18cd9e6
Revises: e4a86bc8ec7b
Create Date: 2023-05-03 17:14:45.696488

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '57fda18cd9e6'
down_revision = 'e4a86bc8ec7b'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('nodes', sa.Column('xray_version', sa.String(length=32), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('nodes', 'xray_version')
    # ### end Alembic commands ###
