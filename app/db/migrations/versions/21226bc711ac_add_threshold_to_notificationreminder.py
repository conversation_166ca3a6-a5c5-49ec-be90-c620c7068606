"""add threshold to NotificationReminder

Revision ID: 21226bc711ac
Revises: 2ea33513efc0
Create Date: 2024-10-18 12:26:30.504491

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '21226bc711ac'
down_revision = '2ea33513efc0'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('notification_reminders', sa.Column('threshold', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('notification_reminders', 'threshold')
    # ### end Alembic commands ###
