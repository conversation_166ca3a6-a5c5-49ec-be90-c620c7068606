@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

.bg-blue-animate {
  animation: blur-animate 200ms ease-in;
  backdrop-filter: blur(10px);
}

@keyframes blur-animate {
  from {
    backdrop-filter: blur(1px);
  }

  to {
    backdrop-filter: blur(10px);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

table thead {
  th:first-of-type {
    border-top-left-radius: 8px;
  }

  th:last-of-type {
    border-top-right-radius: 8px;
  }
}

.last-row {
  td:first-of-type {
    border-bottom-left-radius: 8px;
  }

  td:last-of-type {
    border-bottom-right-radius: 8px;
  }
}

.slick-prev {
  left: -40px;
}

.slick-next {
  right: -40px;
}

.slick-prev,
.slick-next {
  z-index: 100;
}

.chakra-popover__popper {
  z-index: 9999 !important;
}

.inbound-item .chakra-checkbox__label {
  max-width: 100%;
  width: 100%;
}

// data picker
.react-datepicker__navigation.react-datepicker__navigation--previous,
.react-datepicker__navigation.react-datepicker__navigation--next {
  padding-top: 6px;
}

.react-datepicker {
  background-color: var(--chakra-colors-white) !important;
  border: 1px solid var(--chakra-colors-gray-200);
  border-radius: var(--chakra-radii-md) !important;
  position: relative;
}

.chakra-ui-dark .react-datepicker {
  background-color: var(--chakra-colors-gray-700) !important;
  border: 1px solid var(--chakra-colors-gray-600);
  border-radius: var(--chakra-radii-md) !important;
  position: relative;
}

.react-datepicker__header {
  text-align: center;
  background-color: unset;
  position: relative;
  border-bottom: 1px solid var(--chakra-colors-gray-200);
}

.chakra-ui-dark .react-datepicker__header {
  border-bottom: 1px solid var(--chakra-colors-gray-600);
}

.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle::after {
  border-top: none;
  border-bottom-color: var(--chakra-colors-white);
}

.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle::before {
  top: -1px;
  border-bottom-color: var(--chakra-colors-gray-200);
}

.chakra-ui-dark .react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle::after {
  border-top: none;
  border-bottom-color: var(--chakra-colors-gray-700);
}

.chakra-ui-dark .react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle::before {
  top: -1px;
  border-bottom-color: var(--chakra-colors-gray-600);
}

.react-datepicker__current-month,
.react-datepicker__day-name,
.react-datepicker__day {
  color: var(--chakra-colors-chakra-body-text) !important;
}

.chakra-ui-dark .react-datepicker__current-month,
.chakra-ui-dark .react-datepicker__day-name,
.chakra-ui-dark .react-datepicker__day {
  color: var(--chakra-colors-gray-300) !important;
}

.react-datepicker__day:hover {
  border-radius: 0.3rem;
  background-color: var(--chakra-colors-primary-300) !important;
  color: var(--chakra-colors-white) !important;
}

.chakra-ui-dark .react-datepicker__day:hover {
  border-radius: 0.3rem;
  background-color: var(--chakra-colors-primary-500) !important;
  color: var(--chakra-colors-gray-800) !important;
}

.react-datepicker__day--disabled {
  color: var(--chakra-colors-gray-400) !important;
}

.react-datepicker__day--disabled:hover {
  background-color: transparent !important;
  color: var(--chakra-colors-gray-400) !important;
}

.chakra-ui-dark .react-datepicker__day--disabled {
  color: var(--chakra-colors-gray-500) !important;
}

.chakra-ui-dark .react-datepicker__day--disabled:hover {
  background-color: transparent !important;
  color: var(--chakra-colors-gray-500) !important;
}

.react-datepicker__day--keyboard-selected,
.react-datepicker__day--selected,
.react-datepicker__day--in-selecting-range,
.react-datepicker__day--in-range {
  border-radius: 0.3rem;
  background-color: var(--chakra-colors-primary-500) !important;
  color: var(--chakra-colors-white) !important;
}

.chakra-ui-dark .react-datepicker__day--keyboard-selected,
.chakra-ui-dark .react-datepicker__day--selected,
.chakra-ui-dark .react-datepicker__day--in-selecting-range,
.chakra-ui-dark .react-datepicker__day--in-range {
  border-radius: 0.3rem;
  background-color: var(--chakra-colors-primary-200) !important;
  color: var(--chakra-colors-gray-800) !important;
}

.react-datepicker__day--in-selecting-range:not(.react-datepicker__day--in-range) {
  border-radius: 0.3rem;
  background-color: var(--chakra-colors-primary-400) !important;
}

.chakra-ui-dark .react-datepicker__day--in-selecting-range:not(.react-datepicker__day--in-range) {
  border-radius: 0.3rem;
  background-color: var(--chakra-colors-primary-400) !important;
}

.react-datepicker__day--outside-month {
  visibility: hidden;
}

.datepicker-panel .react-datepicker {
  background-color: unset;
  border: unset;
  border-radius: unset;
  display: inline-block;
  position: relative;
}

.green {
  background: #66ff99;
}

.red {
  background: #e53e3e;
}

.orange {
  background: #fbd38d;
}

.bx-shadow {
  box-shadow: 0px 0px 8px 3px rgb(230 221 221 / 10%);
}

.circle {
  min-width: 10px;
  min-height: 10px;
  border-radius: 50%;

  box-shadow: 0px 0px 1px 1px #0000001a;
}

.pulse {
  animation: pulse-animation 3s infinite;
}
.pulse.green {
  animation: green-pulse-animation 3s infinite;
}
.pulse.red{
  animation: red-pulse-animation 3s infinite;
}
.pulse.orange{
  animation: orange-pulse-animation 3s infinite;
}

@keyframes pulse-animation {
  0% {
    box-shadow: 0 0 0 0px rgba(0, 0, 0, 0.4);
  }

  100% {
    box-shadow: 0 0 0 10px rgba(0, 0, 0, 0);
  }
}
@keyframes green-pulse-animation {
  0% {
    box-shadow: 0 0 0 0px #66ff9975;
  }

  100% {
    box-shadow: 0 0 0 10px #66ff9900;
  }
}
@keyframes red-pulse-animation {
  0% {
    box-shadow: 0 0 0 0px #e53e3e8c;
  }

  100% {
    box-shadow: 0 0 0 10px #e53e3e00;
  }
}
@keyframes orange-pulse-animation {
  0% {
    box-shadow: 0 0 0 0px #fbd38d85;
  }

  100% {
    box-shadow: 0 0 0 10px #fbd38d00;
  }
}

.flex-status {
  display: flex;
  align-items: center;
  justify-content: start;
  gap: .7rem;
}