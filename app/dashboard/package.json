{"name": "marzban-dashboard", "private": true, "version": "0.0.0", "scripts": {"dev": "vite --port 3000", "build": "tsc && vite build", "preview": "vite preview --port 3000", "gen:theme-typings": "chakra-cli tokens ./chakra.config.ts", "postinstall": "npm run gen:theme-typings"}, "dependencies": {"@chakra-ui/icons": "^2.0.0", "@chakra-ui/react": "^2.5.5", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@heroicons/react": "^2.0.12", "@hookform/resolvers": "^2.9.10", "antd": "^5.25.4", "apexcharts": "^3.40.0", "axios": "^1.0.0", "classnames": "^2.3.2", "dayjs": "^1.11.6", "framer-motion": "^7.6.6", "i18next": "^22.4.14", "i18next-browser-languagedetector": "^7.0.1", "i18next-http-backend": "^2.2.0", "jsoneditor": "^9.10.1", "lodash.debounce": "^4.0.8", "ofetch": "^1.3.3", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-apexcharts": "^1.4.0", "react-copy-to-clipboard": "^5.1.0", "react-datepicker": "^4.8.0", "react-dom": "^18.2.0", "react-github-btn": "^1.4.0", "react-hook-form": "^7.39.1", "react-i18next": "^12.2.0", "react-json-editor-ajrm": "^2.5.14", "react-loading-skeleton": "^3.1.0", "react-query": "^3.39.3", "react-router-dom": "^6.4.3", "react-slick": "^0.29.0", "react-use-websocket": "^4.3.1", "rollup-plugin-visualizer": "^5.9.0", "slick-carousel": "^1.8.1", "vite-tsconfig-paths": "^4.0.3", "zod": "^3.19.1", "zustand": "^4.4.6", "zustand-computed": "^1.3.3"}, "devDependencies": {"@chakra-ui/cli": "^2.3.0", "@types/jsoneditor": "^9.9.0", "@types/lodash.debounce": "^4.0.7", "@types/react": "^18.0.17", "@types/react-copy-to-clipboard": "^5.0.4", "@types/react-datepicker": "^4.8.0", "@types/react-dom": "^18.0.6", "@types/react-json-editor-ajrm": "^2.5.3", "@types/react-slick": "^0.23.10", "@vitejs/plugin-react": "^2.1.0", "autoprefixer": "^10.4.12", "sass": "^1.57.1", "typescript": "^4.9.5", "vite": "^3.1.0", "vite-plugin-svgr": "^2.2.2"}}