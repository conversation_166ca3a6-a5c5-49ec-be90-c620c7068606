{"active": "active", "activeUsers": "active users", "apply": "Apply", "cancel": "Cancel", "close": "Close", "core.configuration": "Configuration", "core.generalErrorMessage": "Something went wrong, please check the configuration", "core.logs": "Logs", "core.restartCore": "Restart Core", "core.restarting": "Restarting...", "core.save": "Save", "core.socket.closed": "Closed", "core.socket.connected": "Connected", "core.socket.connecting": "Connecting...", "core.socket.not_connected": "Not Connected", "core.successMessage": "Core settings updated successfully", "core.title": "Core Settings", "createNewUser": "Create new user", "createUser": "Create User", "dataUsage": "data usage", "dateFormat": "MMMM d, yyy", "delete": "Delete", "deleteNode.deleteSuccess": "Node {{name}} removed successfully", "deleteNode.prompt": "Are you sure you want to delete the <b>{{name}}</b> node?", "deleteNode.title": "Delete Node", "deleteUser.deleteSuccess": "{{username}} deleted successfully.", "deleteUser.prompt": "Are you sure you want to delete <b>{{username}}</b>?", "deleteUser.title": "Delete User", "disabled": "disabled", "expired": "Expired {{time}} ago", "expires": "Expires in {{time}}", "header.donation": "Donation", "header.hiddifyImport": "Hiddify Import", "header.hostSettings": "Host Settings", "header.resilientNodeGroups": "Resilient Node Groups", "header.logout": "Log out", "header.nodeSettings": "Node Settings", "header.nodesUsage": "Nodes Usage", "hostsDialog.addHost": "Add host", "hostsDialog.advancedOptions": "Advanced options", "hostsDialog.allowinsecure": "Allow Insecure", "hostsDialog.useSniAsHost": "Use sni as host", "hostsDialog.alpn": "ALPN", "hostsDialog.apply": "Apply", "hostsDialog.currentServer": "IP Address of current server", "hostsDialog.currentServerv6": "IPv6 of current server", "hostsDialog.dataLimit": "The usage limit of the user", "hostsDialog.dataUsage": "The current usage of the user", "hostsDialog.desc": "Use these variables to make it dynamic", "hostsDialog.expireDate": "Expiry date of the user", "hostsDialog.fingerprint": "Fingerprint", "hostsDialog.fragment": "Fragment pattern", "hostsDialog.fragment.info": "Correct pattern: length,interval,packets", "hostsDialog.fragment.info.attention": "Attention: currently, this feature only supported in streisand >= 1.6.12 and v2rayNG >= 1.8.16 (custom config)", "hostsDialog.fragment.info.examples": "Examples:", "hostsDialog.noise": "Noise pattern", "hostsDialog.noise.info": "Correct pattern: packets,delay", "hostsDialog.noise.info.attention": "Attention: currently, this feature only supported in streisand >= 1.6.32 and v2rayNG >= 1.8.39 (custom config)", "hostsDialog.noise.info.examples": "Examples:", "hostsDialog.host": "Request Host", "hostsDialog.host.info": "By default, if a request host is set in the Xray config, this host is used. However, you can set a custom request host here if needed.", "hostsDialog.host.multiHost": "To set multiple addresses, separate them with <badge>,</badge> Each time an address is chosen randomly.", "hostsDialog.host.wildcard": "Use <badge>*</badge> to generate a random string (works for wildcard domain names)", "hostsDialog.jalaliExpireDate": "Expiry date of the user in solar calendar", "hostsDialog.loading": "loading...", "hostsDialog.muxEnable": "Enable MUX", "hostsDialog.path": "Path", "hostsDialog.path.info": "Set a path for host users, useful behind a reverse proxy.", "hostsDialog.port": "Port", "hostsDialog.port.info": "By default, a host uses the default port of the inbound. You can set a custom port in case this host is a server that forwards traffic from a port that differs from your server's port. For example, the server may forward traffic from port 8443 to the default port of your inbound server.", "hostsDialog.proxyMethod": "Proxy transport method (e.g. ws)", "hostsDialog.proxyOutbound": "Proxy outbound json", "hostsDialog.proxyOutbound.info": "Extra outbound ( only in v2ray custom config )", "hostsDialog.proxyProtocol": "Proxy protocol (e.g. VMess)", "hostsDialog.randomUserAgent": "Use random user agent", "hostsDialog.remainingData": "Remaining data of the user", "hostsDialog.remainingDays": "Remaining days of the user", "hostsDialog.remainingTime": "Remaining time of the user", "hostsDialog.savedSuccess": "Hosts saved successfully", "hostsDialog.security": "Security Layer", "hostsDialog.security.info": "If the middleware server of this host uses a different security layer than the inbound's default, you can set a custom security layer here.", "hostsDialog.sni": "SNI", "hostsDialog.sni.info": "By default, a host uses the default SNI of the inbound. You can set a custom SNI in case this host is a server that has a different SNI. For example, the server may receive traffic with a different SSL certificate, perform SSL termination, and forward it to your inbound server.", "hostsDialog.sockopt": "<PERSON><PERSON><PERSON><PERSON>", "hostsDialog.statusEmoji": "User status as an emoji (✅,⌛️,🪫,❌,🔌)", "hostsDialog.statusText": "User status", "hostsDialog.title": "Using this setting, you are able to assign specific address for each inbound.", "hostsDialog.username": "The username of the user", "inbound": "Inbound", "itemsPerPage": "Items per page", "login": "<PERSON><PERSON>", "login.fieldRequired": "This field is required", "login.loginYourAccount": "Login to your account", "login.welcomeBack": "Welcome back, please enter your details", "memoryUsage": "memory usage", "next": "Next", "nodes.addHostForEveryInbound": "Add this node as a new host for every inbound", "nodes.addNewMarzbanNode": "Add New Marzban Node", "nodes.addNode": "Add Node", "nodes.addNodeSuccess": "Node {{name}} added successfully", "nodes.apply": "editNode", "nodes.certificate": "Certificate", "nodes.connection-hint": "To setup a Marzban Node, you need to set this certificate on the node to initialize a secure connection between main server and the node", "nodes.download-certificate": "Download certificate", "nodes.editNode": "Update Node", "nodes.hide-certificate": "Hide certificate", "nodes.nodeAPIPort": "API Port", "nodes.nodeAddress": "Address", "nodes.nodeName": "Name", "nodes.nodePort": "Port", "nodes.reconnect": "Reconnect", "nodes.reconnecting": "Reconnecting...", "nodes.show-certificate": "Show certificate", "nodes.title": "Using Marzban-Node, you are able to scale up your connection quality by adding different nodes on different servers.", "nodes.usageCoefficient": "Usage Ratio", "on_hold": "On Hold", "password": "Password", "previous": "Previous", "qrcodeDialog.sublink": "Subscribe Link", "reset": "Reset", "resetAllUsage": "Reset All Usages", "resetAllUsage.error": "Usage reset failed, please try again.", "resetAllUsage.prompt": "This action clears all of the users' data usage completely. Are you sure you want to reset all usage? THIS CANNOT BE UNDONE!", "resetAllUsage.success": "All usage has reset successfully.", "resetAllUsage.title": "Reset data usage for all users", "resetUserUsage.error": "Usage reset failed, please try again.", "resetUserUsage.prompt": "Are you sure you want to reset <b>{{username}}</b>'s usage?", "resetUserUsage.success": "{{username}}'s usage has reset successfully.", "resetUserUsage.title": "Reset User Usage", "revoke": "Revoke", "revokeUserSub.error": "Subscription revoke failed, please try again.", "revokeUserSub.prompt": "Are you sure you want to revoke <b>{{username}}</b>'s subscription?", "revokeUserSub.success": "{{username}}'s subscription has revoked successfully.", "revokeUserSub.title": "Revoke User Subscription", "search": "Search", "status.active": "Active", "status.disabled": "Disabled", "status.expired": "Expired", "status.limited": "Limited", "status.on_hold": "On Hold", "nodeModal.status.error": "Error", "nodeModal.status.disabled": "Disabled", "nodeModal.status.connecting": "Connecting", "nodeModal.status.connected": "Connected", "userDialog.absolute": "Absolute", "userDialog.custom": "Custom", "userDialog.dataLimit": "Data Limit", "userDialog.days": "Days", "userDialog.editUser": "Edit user", "userDialog.editUserTitle": "Edit user", "userDialog.endDate": "End date", "userDialog.expiryDate": "Expiry Date", "userDialog.generatedByDefault": "Automatically generated by default", "userDialog.hours": "Hours", "userDialog.method": "Method", "userDialog.months": "Months", "userDialog.note": "Note", "userDialog.onHold": "On Hold", "userDialog.onHoldExpireDuration": "Expire Duration", "userDialog.optional": "optional", "userDialog.periodicUsageReset": "Periodic Usage Reset", "userDialog.protocols": "Protocols", "userDialog.relative": "Relative", "userDialog.resetStrategyAnnually": "Annually", "userDialog.resetStrategyDaily": "Daily", "userDialog.resetStrategyMonthly": "Monthly", "userDialog.resetStrategyNo": "No", "userDialog.resetStrategyWeekly": "Weekly", "userDialog.resetUsage": "Reset Usage", "userDialog.revokeSubscription": "Revoke Subscription", "userDialog.selectOneProtocol": "Please select at least one protocol", "userDialog.shadowsocksDesc": "A proxy protocol designed to circumvent internet censorship", "userDialog.startDate": "Start date", "userDialog.total": "Total: ", "userDialog.trojanDesc": "A protocol that mimics HTTPS traffic", "userDialog.usage": "Usage", "userDialog.userAlreadyExists": "User already exists", "userDialog.userCreated": "User {{username}} created.", "userDialog.userEdited": "User {{username}} edited.", "userDialog.vlessDesc": "A lightweight protocol with minimal resource usage", "userDialog.vmessDesc": "A protocol for enhanced security and performance", "userDialog.warningNoProtocol": "Please select at least one protocol", "userDialog.weeks": "Weeks", "username": "Username", "users": "Users", "usersTable.copied": "<PERSON>pied", "usersTable.copyConfigs": "Copy Configs", "usersTable.copyLink": "Copy Subscription Link", "usersTable.dataUsage": "data usage", "usersTable.noUser": "There is no user added to the system", "usersTable.noUserMatched": "It seems there is no user matched with what you are looking for", "usersTable.status": "status", "usersTable.total": "Total", "hiddifyImport.title": "Import Users from Hiddify", "hiddifyImport.selectFile": "Select Hiddify Backup File", "hiddifyImport.fileSelected": "File selected: {{filename}}", "hiddifyImport.invalidFile": "Invalid File", "hiddifyImport.invalidFileDesc": "Please select a valid JSON file.", "hiddifyImport.unlimitedExpiration": "Set unlimited expiration for all users", "hiddifyImport.unlimitedExpirationDesc": "If enabled, all imported users will have unlimited expiration (expire = 0), ignoring package_days from Hiddify.", "hiddifyImport.smartUsernameParsing": "Enable smart username & note parsing", "hiddifyImport.smartUsernameParsingDesc": "If enabled, names like '1234 <PERSON>' will be split: '1234' as username, '<PERSON>' as note. Otherwise, the full name will be used as username.", "hiddifyImport.protocolSelection": "Select Protocols", "hiddifyImport.protocolSelectionDesc": "Choose which protocols the imported users should have access to.", "hiddifyImport.noFileSelected": "No File Selected", "hiddifyImport.noProtocolsSelected": "No Protocols Selected", "hiddifyImport.importing": "Importing users...", "hiddifyImport.importUsers": "Import Users", "hiddifyImport.importComplete": "Import Complete", "hiddifyImport.importStats": "{{successful}} users imported successfully, {{failed}} failed.", "hiddifyImport.importSuccess": "Import Successful", "hiddifyImport.importSuccessDesc": "{{successful}} users imported successfully. {{failed}} users failed to import.", "hiddifyImport.importWarning": "Import Completed with Warnings", "hiddifyImport.importWarningDesc": "{{failed}} users failed to import. Check the details below.", "hiddifyImport.importError": "Import Failed", "hiddifyImport.importErrorDesc": "An error occurred during import. Please try again.", "hiddifyImport.deleteImported": "Delete Imported", "hiddifyImport.deleteConfirmTitle": "Delete Imported Users", "hiddifyImport.deleteConfirmMessage": "Are you sure you want to delete {{count}} users that were imported from Hiddify?", "hiddifyImport.deleteConfirmDesc": "This action cannot be undone. All imported users and their data will be permanently removed.", "hiddifyImport.confirmDelete": "Delete Users", "hiddifyImport.deleteSuccess": "Deletion Successful", "hiddifyImport.deleteSuccessDesc": "{{count}} imported users have been successfully deleted.", "hiddifyImport.deleteError": "Deletion Failed", "hiddifyImport.deleteErrorDesc": "An error occurred while deleting imported users. Please try again.", "hiddifyImport.noImportedUsers": "No Imported Users", "hiddifyImport.noImportedUsersDesc": "There are no users imported from Hiddify to delete.", "userDialog.unlimitedData": "Unlimited Data"}