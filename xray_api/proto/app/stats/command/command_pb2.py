# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: app/stats/command/command.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1f\x61pp/stats/command/command.proto\x12\x16xray.app.stats.command\".\n\x0fGetStatsRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05reset\x18\x02 \x01(\x08\"#\n\x04Stat\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x03\">\n\x10GetStatsResponse\x12*\n\x04stat\x18\x01 \x01(\x0b\x32\x1c.xray.app.stats.command.Stat\"3\n\x11QueryStatsRequest\x12\x0f\n\x07pattern\x18\x01 \x01(\t\x12\r\n\x05reset\x18\x02 \x01(\x08\"@\n\x12QueryStatsResponse\x12*\n\x04stat\x18\x01 \x03(\x0b\x32\x1c.xray.app.stats.command.Stat\"\x11\n\x0fSysStatsRequest\"\xc2\x01\n\x10SysStatsResponse\x12\x14\n\x0cNumGoroutine\x18\x01 \x01(\r\x12\r\n\x05NumGC\x18\x02 \x01(\r\x12\r\n\x05\x41lloc\x18\x03 \x01(\x04\x12\x12\n\nTotalAlloc\x18\x04 \x01(\x04\x12\x0b\n\x03Sys\x18\x05 \x01(\x04\x12\x0f\n\x07Mallocs\x18\x06 \x01(\x04\x12\r\n\x05\x46rees\x18\x07 \x01(\x04\x12\x13\n\x0bLiveObjects\x18\x08 \x01(\x04\x12\x14\n\x0cPauseTotalNs\x18\t \x01(\x04\x12\x0e\n\x06Uptime\x18\n \x01(\r\"\x08\n\x06\x43onfig2\xba\x02\n\x0cStatsService\x12_\n\x08GetStats\x12\'.xray.app.stats.command.GetStatsRequest\x1a(.xray.app.stats.command.GetStatsResponse\"\x00\x12\x65\n\nQueryStats\x12).xray.app.stats.command.QueryStatsRequest\x1a*.xray.app.stats.command.QueryStatsResponse\"\x00\x12\x62\n\x0bGetSysStats\x12\'.xray.app.stats.command.SysStatsRequest\x1a(.xray.app.stats.command.SysStatsResponse\"\x00\x42\x64\n\x1a\x63om.xray.app.stats.commandP\x01Z+github.com/xtls/xray-core/app/stats/command\xaa\x02\x16Xray.App.Stats.Commandb\x06proto3')



_GETSTATSREQUEST = DESCRIPTOR.message_types_by_name['GetStatsRequest']
_STAT = DESCRIPTOR.message_types_by_name['Stat']
_GETSTATSRESPONSE = DESCRIPTOR.message_types_by_name['GetStatsResponse']
_QUERYSTATSREQUEST = DESCRIPTOR.message_types_by_name['QueryStatsRequest']
_QUERYSTATSRESPONSE = DESCRIPTOR.message_types_by_name['QueryStatsResponse']
_SYSSTATSREQUEST = DESCRIPTOR.message_types_by_name['SysStatsRequest']
_SYSSTATSRESPONSE = DESCRIPTOR.message_types_by_name['SysStatsResponse']
_CONFIG = DESCRIPTOR.message_types_by_name['Config']
GetStatsRequest = _reflection.GeneratedProtocolMessageType('GetStatsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETSTATSREQUEST,
  '__module__' : 'app.stats.command.command_pb2'
  # @@protoc_insertion_point(class_scope:xray.app.stats.command.GetStatsRequest)
  })
_sym_db.RegisterMessage(GetStatsRequest)

Stat = _reflection.GeneratedProtocolMessageType('Stat', (_message.Message,), {
  'DESCRIPTOR' : _STAT,
  '__module__' : 'app.stats.command.command_pb2'
  # @@protoc_insertion_point(class_scope:xray.app.stats.command.Stat)
  })
_sym_db.RegisterMessage(Stat)

GetStatsResponse = _reflection.GeneratedProtocolMessageType('GetStatsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETSTATSRESPONSE,
  '__module__' : 'app.stats.command.command_pb2'
  # @@protoc_insertion_point(class_scope:xray.app.stats.command.GetStatsResponse)
  })
_sym_db.RegisterMessage(GetStatsResponse)

QueryStatsRequest = _reflection.GeneratedProtocolMessageType('QueryStatsRequest', (_message.Message,), {
  'DESCRIPTOR' : _QUERYSTATSREQUEST,
  '__module__' : 'app.stats.command.command_pb2'
  # @@protoc_insertion_point(class_scope:xray.app.stats.command.QueryStatsRequest)
  })
_sym_db.RegisterMessage(QueryStatsRequest)

QueryStatsResponse = _reflection.GeneratedProtocolMessageType('QueryStatsResponse', (_message.Message,), {
  'DESCRIPTOR' : _QUERYSTATSRESPONSE,
  '__module__' : 'app.stats.command.command_pb2'
  # @@protoc_insertion_point(class_scope:xray.app.stats.command.QueryStatsResponse)
  })
_sym_db.RegisterMessage(QueryStatsResponse)

SysStatsRequest = _reflection.GeneratedProtocolMessageType('SysStatsRequest', (_message.Message,), {
  'DESCRIPTOR' : _SYSSTATSREQUEST,
  '__module__' : 'app.stats.command.command_pb2'
  # @@protoc_insertion_point(class_scope:xray.app.stats.command.SysStatsRequest)
  })
_sym_db.RegisterMessage(SysStatsRequest)

SysStatsResponse = _reflection.GeneratedProtocolMessageType('SysStatsResponse', (_message.Message,), {
  'DESCRIPTOR' : _SYSSTATSRESPONSE,
  '__module__' : 'app.stats.command.command_pb2'
  # @@protoc_insertion_point(class_scope:xray.app.stats.command.SysStatsResponse)
  })
_sym_db.RegisterMessage(SysStatsResponse)

Config = _reflection.GeneratedProtocolMessageType('Config', (_message.Message,), {
  'DESCRIPTOR' : _CONFIG,
  '__module__' : 'app.stats.command.command_pb2'
  # @@protoc_insertion_point(class_scope:xray.app.stats.command.Config)
  })
_sym_db.RegisterMessage(Config)

_STATSSERVICE = DESCRIPTOR.services_by_name['StatsService']
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\032com.xray.app.stats.commandP\001Z+github.com/xtls/xray-core/app/stats/command\252\002\026Xray.App.Stats.Command'
  _GETSTATSREQUEST._serialized_start=59
  _GETSTATSREQUEST._serialized_end=105
  _STAT._serialized_start=107
  _STAT._serialized_end=142
  _GETSTATSRESPONSE._serialized_start=144
  _GETSTATSRESPONSE._serialized_end=206
  _QUERYSTATSREQUEST._serialized_start=208
  _QUERYSTATSREQUEST._serialized_end=259
  _QUERYSTATSRESPONSE._serialized_start=261
  _QUERYSTATSRESPONSE._serialized_end=325
  _SYSSTATSREQUEST._serialized_start=327
  _SYSSTATSREQUEST._serialized_end=344
  _SYSSTATSRESPONSE._serialized_start=347
  _SYSSTATSRESPONSE._serialized_end=541
  _CONFIG._serialized_start=543
  _CONFIG._serialized_end=551
  _STATSSERVICE._serialized_start=554
  _STATSSERVICE._serialized_end=868
# @@protoc_insertion_point(module_scope)
