# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: app/metrics/config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x18\x61pp/metrics/config.proto\x12\x10xray.app.metrics\"\x15\n\x06\x43onfig\x12\x0b\n\x03tag\x18\x01 \x01(\tBR\n\x14\x63om.xray.app.metricsP\x01Z%github.com/xtls/xray-core/app/metrics\xaa\x02\x10Xray.App.Metricsb\x06proto3')



_CONFIG = DESCRIPTOR.message_types_by_name['Config']
Config = _reflection.GeneratedProtocolMessageType('Config', (_message.Message,), {
  'DESCRIPTOR' : _CONFIG,
  '__module__' : 'app.metrics.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.app.metrics.Config)
  })
_sym_db.RegisterMessage(Config)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\024com.xray.app.metricsP\001Z%github.com/xtls/xray-core/app/metrics\252\002\020Xray.App.Metrics'
  _CONFIG._serialized_start=46
  _CONFIG._serialized_end=67
# @@protoc_insertion_point(module_scope)
