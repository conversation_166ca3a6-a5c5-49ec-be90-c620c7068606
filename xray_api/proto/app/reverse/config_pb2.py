# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: app/reverse/config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x18\x61pp/reverse/config.proto\x12\x10xray.app.reverse\"i\n\x07\x43ontrol\x12.\n\x05state\x18\x01 \x01(\x0e\x32\x1f.xray.app.reverse.Control.State\x12\x0e\n\x06random\x18\x63 \x01(\x0c\"\x1e\n\x05State\x12\n\n\x06\x41\x43TIVE\x10\x00\x12\t\n\x05\x44RAIN\x10\x01\"+\n\x0c\x42ridgeConfig\x12\x0b\n\x03tag\x18\x01 \x01(\t\x12\x0e\n\x06\x64omain\x18\x02 \x01(\t\"+\n\x0cPortalConfig\x12\x0b\n\x03tag\x18\x01 \x01(\t\x12\x0e\n\x06\x64omain\x18\x02 \x01(\t\"v\n\x06\x43onfig\x12\x35\n\rbridge_config\x18\x01 \x03(\x0b\x32\x1e.xray.app.reverse.BridgeConfig\x12\x35\n\rportal_config\x18\x02 \x03(\x0b\x32\x1e.xray.app.reverse.PortalConfigBV\n\x16\x63om.xray.proxy.reverseP\x01Z%github.com/xtls/xray-core/app/reverse\xaa\x02\x12Xray.Proxy.Reverseb\x06proto3')



_CONTROL = DESCRIPTOR.message_types_by_name['Control']
_BRIDGECONFIG = DESCRIPTOR.message_types_by_name['BridgeConfig']
_PORTALCONFIG = DESCRIPTOR.message_types_by_name['PortalConfig']
_CONFIG = DESCRIPTOR.message_types_by_name['Config']
_CONTROL_STATE = _CONTROL.enum_types_by_name['State']
Control = _reflection.GeneratedProtocolMessageType('Control', (_message.Message,), {
  'DESCRIPTOR' : _CONTROL,
  '__module__' : 'app.reverse.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.app.reverse.Control)
  })
_sym_db.RegisterMessage(Control)

BridgeConfig = _reflection.GeneratedProtocolMessageType('BridgeConfig', (_message.Message,), {
  'DESCRIPTOR' : _BRIDGECONFIG,
  '__module__' : 'app.reverse.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.app.reverse.BridgeConfig)
  })
_sym_db.RegisterMessage(BridgeConfig)

PortalConfig = _reflection.GeneratedProtocolMessageType('PortalConfig', (_message.Message,), {
  'DESCRIPTOR' : _PORTALCONFIG,
  '__module__' : 'app.reverse.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.app.reverse.PortalConfig)
  })
_sym_db.RegisterMessage(PortalConfig)

Config = _reflection.GeneratedProtocolMessageType('Config', (_message.Message,), {
  'DESCRIPTOR' : _CONFIG,
  '__module__' : 'app.reverse.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.app.reverse.Config)
  })
_sym_db.RegisterMessage(Config)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\026com.xray.proxy.reverseP\001Z%github.com/xtls/xray-core/app/reverse\252\002\022Xray.Proxy.Reverse'
  _CONTROL._serialized_start=46
  _CONTROL._serialized_end=151
  _CONTROL_STATE._serialized_start=121
  _CONTROL_STATE._serialized_end=151
  _BRIDGECONFIG._serialized_start=153
  _BRIDGECONFIG._serialized_end=196
  _PORTALCONFIG._serialized_start=198
  _PORTALCONFIG._serialized_end=241
  _CONFIG._serialized_start=243
  _CONFIG._serialized_end=361
# @@protoc_insertion_point(module_scope)
