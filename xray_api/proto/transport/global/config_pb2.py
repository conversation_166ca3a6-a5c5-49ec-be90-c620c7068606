# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: transport/global/config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from xray_api.proto.transport.internet import config_pb2 as transport_dot_internet_dot_config__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1dtransport/global/config.proto\x12\x0exray.transport\x1a\x1ftransport/internet/config.proto\"R\n\x06\x43onfig\x12\x44\n\x12transport_settings\x18\x01 \x03(\x0b\x32(.xray.transport.internet.TransportConfig:\x02\x18\x01\x42\x61\n\x19\x63om.xray.transport.globalP\x01Z*github.com/xtls/xray-core/transport/global\xaa\x02\x15Xray.Transport.Globalb\x06proto3')



_CONFIG = DESCRIPTOR.message_types_by_name['Config']
Config = _reflection.GeneratedProtocolMessageType('Config', (_message.Message,), {
  'DESCRIPTOR' : _CONFIG,
  '__module__' : 'transport.global.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.transport.Config)
  })
_sym_db.RegisterMessage(Config)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\031com.xray.transport.globalP\001Z*github.com/xtls/xray-core/transport/global\252\002\025Xray.Transport.Global'
  _CONFIG._options = None
  _CONFIG._serialized_options = b'\030\001'
  _CONFIG._serialized_start=82
  _CONFIG._serialized_end=164
# @@protoc_insertion_point(module_scope)
