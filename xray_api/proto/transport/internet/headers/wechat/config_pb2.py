# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: transport/internet/headers/wechat/config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n.transport/internet/headers/wechat/config.proto\x12&xray.transport.internet.headers.wechat\"\r\n\x0bVideoConfigB\x94\x01\n*com.xray.transport.internet.headers.wechatP\x01Z;github.com/xtls/xray-core/transport/internet/headers/wechat\xaa\x02&Xray.Transport.Internet.Headers.Wechatb\x06proto3')



_VIDEOCONFIG = DESCRIPTOR.message_types_by_name['VideoConfig']
VideoConfig = _reflection.GeneratedProtocolMessageType('VideoConfig', (_message.Message,), {
  'DESCRIPTOR' : _VIDEOCONFIG,
  '__module__' : 'transport.internet.headers.wechat.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.transport.internet.headers.wechat.VideoConfig)
  })
_sym_db.RegisterMessage(VideoConfig)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n*com.xray.transport.internet.headers.wechatP\001Z;github.com/xtls/xray-core/transport/internet/headers/wechat\252\002&Xray.Transport.Internet.Headers.Wechat'
  _VIDEOCONFIG._serialized_start=90
  _VIDEOCONFIG._serialized_end=103
# @@protoc_insertion_point(module_scope)
