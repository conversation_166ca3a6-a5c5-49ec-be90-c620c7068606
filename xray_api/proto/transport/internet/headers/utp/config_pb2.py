# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: transport/internet/headers/utp/config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n+transport/internet/headers/utp/config.proto\x12#xray.transport.internet.headers.utp\"\x19\n\x06\x43onfig\x12\x0f\n\x07version\x18\x01 \x01(\rB\x8b\x01\n\'com.xray.transport.internet.headers.utpP\x01Z8github.com/xtls/xray-core/transport/internet/headers/utp\xaa\x02#Xray.Transport.Internet.Headers.Utpb\x06proto3')



_CONFIG = DESCRIPTOR.message_types_by_name['Config']
Config = _reflection.GeneratedProtocolMessageType('Config', (_message.Message,), {
  'DESCRIPTOR' : _CONFIG,
  '__module__' : 'transport.internet.headers.utp.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.transport.internet.headers.utp.Config)
  })
_sym_db.RegisterMessage(Config)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\'com.xray.transport.internet.headers.utpP\001Z8github.com/xtls/xray-core/transport/internet/headers/utp\252\002#Xray.Transport.Internet.Headers.Utp'
  _CONFIG._serialized_start=84
  _CONFIG._serialized_end=109
# @@protoc_insertion_point(module_scope)
