# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: transport/internet/headers/dns/config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n+transport/internet/headers/dns/config.proto\x12#xray.transport.internet.headers.dns\"\x18\n\x06\x43onfig\x12\x0e\n\x06\x64omain\x18\x01 \x01(\tB\x8b\x01\n\'com.xray.transport.internet.headers.dnsP\x01Z8github.com/xtls/xray-core/transport/internet/headers/dns\xaa\x02#Xray.Transport.Internet.Headers.DNSb\x06proto3')



_CONFIG = DESCRIPTOR.message_types_by_name['Config']
Config = _reflection.GeneratedProtocolMessageType('Config', (_message.Message,), {
  'DESCRIPTOR' : _CONFIG,
  '__module__' : 'transport.internet.headers.dns.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.transport.internet.headers.dns.Config)
  })
_sym_db.RegisterMessage(Config)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\'com.xray.transport.internet.headers.dnsP\001Z8github.com/xtls/xray-core/transport/internet/headers/dns\252\002#Xray.Transport.Internet.Headers.DNS'
  _CONFIG._serialized_start=84
  _CONFIG._serialized_end=108
# @@protoc_insertion_point(module_scope)
