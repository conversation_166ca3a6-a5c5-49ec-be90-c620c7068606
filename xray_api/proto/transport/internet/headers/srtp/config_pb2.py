# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: transport/internet/headers/srtp/config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n,transport/internet/headers/srtp/config.proto\x12$xray.transport.internet.headers.srtp\"w\n\x06\x43onfig\x12\x0f\n\x07version\x18\x01 \x01(\r\x12\x0f\n\x07padding\x18\x02 \x01(\x08\x12\x11\n\textension\x18\x03 \x01(\x08\x12\x12\n\ncsrc_count\x18\x04 \x01(\r\x12\x0e\n\x06marker\x18\x05 \x01(\x08\x12\x14\n\x0cpayload_type\x18\x06 \x01(\rB\x8e\x01\n(com.xray.transport.internet.headers.srtpP\x01Z9github.com/xtls/xray-core/transport/internet/headers/srtp\xaa\x02$Xray.Transport.Internet.Headers.Srtpb\x06proto3')



_CONFIG = DESCRIPTOR.message_types_by_name['Config']
Config = _reflection.GeneratedProtocolMessageType('Config', (_message.Message,), {
  'DESCRIPTOR' : _CONFIG,
  '__module__' : 'transport.internet.headers.srtp.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.transport.internet.headers.srtp.Config)
  })
_sym_db.RegisterMessage(Config)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n(com.xray.transport.internet.headers.srtpP\001Z9github.com/xtls/xray-core/transport/internet/headers/srtp\252\002$Xray.Transport.Internet.Headers.Srtp'
  _CONFIG._serialized_start=86
  _CONFIG._serialized_end=205
# @@protoc_insertion_point(module_scope)
