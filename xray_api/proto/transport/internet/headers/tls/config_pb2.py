# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: transport/internet/headers/tls/config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n+transport/internet/headers/tls/config.proto\x12#xray.transport.internet.headers.tls\"\x0e\n\x0cPacketConfigB\x8b\x01\n\'com.xray.transport.internet.headers.tlsP\x01Z8github.com/xtls/xray-core/transport/internet/headers/tls\xaa\x02#Xray.Transport.Internet.Headers.Tlsb\x06proto3')



_PACKETCONFIG = DESCRIPTOR.message_types_by_name['PacketConfig']
PacketConfig = _reflection.GeneratedProtocolMessageType('PacketConfig', (_message.Message,), {
  'DESCRIPTOR' : _PACKETCONFIG,
  '__module__' : 'transport.internet.headers.tls.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.transport.internet.headers.tls.PacketConfig)
  })
_sym_db.RegisterMessage(PacketConfig)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\'com.xray.transport.internet.headers.tlsP\001Z8github.com/xtls/xray-core/transport/internet/headers/tls\252\002#Xray.Transport.Internet.Headers.Tls'
  _PACKETCONFIG._serialized_start=84
  _PACKETCONFIG._serialized_end=98
# @@protoc_insertion_point(module_scope)
