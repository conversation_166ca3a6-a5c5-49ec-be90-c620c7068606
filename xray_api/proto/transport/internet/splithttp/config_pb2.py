# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: transport/internet/splithttp/config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n)transport/internet/splithttp/config.proto\x12!xray.transport.internet.splithttp\"\xcf\x01\n\x06\x43onfig\x12\x0c\n\x04host\x18\x01 \x01(\t\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\x45\n\x06header\x18\x03 \x03(\x0b\x32\x35.xray.transport.internet.splithttp.Config.HeaderEntry\x12\x1c\n\x14maxConcurrentUploads\x18\x04 \x01(\x05\x12\x15\n\rmaxUploadSize\x18\x05 \x01(\x05\x1a-\n\x0bHeaderEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x42\x85\x01\n%com.xray.transport.internet.splithttpP\x01Z6github.com/xtls/xray-core/transport/internet/splithttp\xaa\x02!Xray.Transport.Internet.SplitHttpb\x06proto3')



_CONFIG = DESCRIPTOR.message_types_by_name['Config']
_CONFIG_HEADERENTRY = _CONFIG.nested_types_by_name['HeaderEntry']
Config = _reflection.GeneratedProtocolMessageType('Config', (_message.Message,), {

  'HeaderEntry' : _reflection.GeneratedProtocolMessageType('HeaderEntry', (_message.Message,), {
    'DESCRIPTOR' : _CONFIG_HEADERENTRY,
    '__module__' : 'transport.internet.splithttp.config_pb2'
    # @@protoc_insertion_point(class_scope:xray.transport.internet.splithttp.Config.HeaderEntry)
    })
  ,
  'DESCRIPTOR' : _CONFIG,
  '__module__' : 'transport.internet.splithttp.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.transport.internet.splithttp.Config)
  })
_sym_db.RegisterMessage(Config)
_sym_db.RegisterMessage(Config.HeaderEntry)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n%com.xray.transport.internet.splithttpP\001Z6github.com/xtls/xray-core/transport/internet/splithttp\252\002!Xray.Transport.Internet.SplitHttp'
  _CONFIG_HEADERENTRY._options = None
  _CONFIG_HEADERENTRY._serialized_options = b'8\001'
  _CONFIG._serialized_start=81
  _CONFIG._serialized_end=288
  _CONFIG_HEADERENTRY._serialized_start=243
  _CONFIG_HEADERENTRY._serialized_end=288
# @@protoc_insertion_point(module_scope)
