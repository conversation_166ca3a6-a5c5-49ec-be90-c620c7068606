# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: transport/internet/udp/config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n#transport/internet/udp/config.proto\x12\x1bxray.transport.internet.udp\"\x08\n\x06\x43onfigBs\n\x1f\x63om.xray.transport.internet.udpP\x01Z0github.com/xtls/xray-core/transport/internet/udp\xaa\x02\x1bXray.Transport.Internet.Udpb\x06proto3')



_CONFIG = DESCRIPTOR.message_types_by_name['Config']
Config = _reflection.GeneratedProtocolMessageType('Config', (_message.Message,), {
  'DESCRIPTOR' : _CONFIG,
  '__module__' : 'transport.internet.udp.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.transport.internet.udp.Config)
  })
_sym_db.RegisterMessage(Config)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\037com.xray.transport.internet.udpP\001Z0github.com/xtls/xray-core/transport/internet/udp\252\002\033Xray.Transport.Internet.Udp'
  _CONFIG._serialized_start=68
  _CONFIG._serialized_end=76
# @@protoc_insertion_point(module_scope)
