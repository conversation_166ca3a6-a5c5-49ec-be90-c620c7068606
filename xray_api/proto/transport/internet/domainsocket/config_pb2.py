# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: transport/internet/domainsocket/config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n,transport/internet/domainsocket/config.proto\x12$xray.transport.internet.domainsocket\"9\n\x06\x43onfig\x12\x0c\n\x04path\x18\x01 \x01(\t\x12\x10\n\x08\x61\x62stract\x18\x02 \x01(\x08\x12\x0f\n\x07padding\x18\x03 \x01(\x08\x42\x8e\x01\n(com.xray.transport.internet.domainsocketP\x01Z9github.com/xtls/xray-core/transport/internet/domainsocket\xaa\x02$Xray.Transport.Internet.DomainSocketb\x06proto3')



_CONFIG = DESCRIPTOR.message_types_by_name['Config']
Config = _reflection.GeneratedProtocolMessageType('Config', (_message.Message,), {
  'DESCRIPTOR' : _CONFIG,
  '__module__' : 'transport.internet.domainsocket.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.transport.internet.domainsocket.Config)
  })
_sym_db.RegisterMessage(Config)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n(com.xray.transport.internet.domainsocketP\001Z9github.com/xtls/xray-core/transport/internet/domainsocket\252\002$Xray.Transport.Internet.DomainSocket'
  _CONFIG._serialized_start=86
  _CONFIG._serialized_end=143
# @@protoc_insertion_point(module_scope)
