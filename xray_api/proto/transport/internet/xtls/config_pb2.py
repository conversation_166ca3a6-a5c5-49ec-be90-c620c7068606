# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: transport/internet/xtls/config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n$transport/internet/xtls/config.proto\x12\x1cxray.transport.internet.xtls\"\x92\x02\n\x0b\x43\x65rtificate\x12\x13\n\x0b\x63\x65rtificate\x18\x01 \x01(\x0c\x12\x0b\n\x03key\x18\x02 \x01(\x0c\x12>\n\x05usage\x18\x03 \x01(\x0e\x32/.xray.transport.internet.xtls.Certificate.Usage\x12\x15\n\rocsp_stapling\x18\x04 \x01(\x04\x12\x18\n\x10\x63\x65rtificate_path\x18\x05 \x01(\t\x12\x10\n\x08key_path\x18\x06 \x01(\t\x12\x18\n\x10One_time_loading\x18\x07 \x01(\x08\"D\n\x05Usage\x12\x10\n\x0c\x45NCIPHERMENT\x10\x00\x12\x14\n\x10\x41UTHORITY_VERIFY\x10\x01\x12\x13\n\x0f\x41UTHORITY_ISSUE\x10\x02\"\xfc\x02\n\x06\x43onfig\x12\x16\n\x0e\x61llow_insecure\x18\x01 \x01(\x08\x12>\n\x0b\x63\x65rtificate\x18\x02 \x03(\x0b\x32).xray.transport.internet.xtls.Certificate\x12\x13\n\x0bserver_name\x18\x03 \x01(\t\x12\x15\n\rnext_protocol\x18\x04 \x03(\t\x12!\n\x19\x65nable_session_resumption\x18\x05 \x01(\x08\x12\x1b\n\x13\x64isable_system_root\x18\x06 \x01(\x08\x12\x13\n\x0bmin_version\x18\x07 \x01(\t\x12\x13\n\x0bmax_version\x18\x08 \x01(\t\x12\x15\n\rcipher_suites\x18\t \x01(\t\x12#\n\x1bprefer_server_cipher_suites\x18\n \x01(\x08\x12\x1a\n\x12reject_unknown_sni\x18\x0c \x01(\x08\x12,\n$pinned_peer_certificate_chain_sha256\x18\r \x03(\x0c\x42v\n com.xray.transport.internet.xtlsP\x01Z1github.com/xtls/xray-core/transport/internet/xtls\xaa\x02\x1cXray.Transport.Internet.Xtlsb\x06proto3')



_CERTIFICATE = DESCRIPTOR.message_types_by_name['Certificate']
_CONFIG = DESCRIPTOR.message_types_by_name['Config']
_CERTIFICATE_USAGE = _CERTIFICATE.enum_types_by_name['Usage']
Certificate = _reflection.GeneratedProtocolMessageType('Certificate', (_message.Message,), {
  'DESCRIPTOR' : _CERTIFICATE,
  '__module__' : 'transport.internet.xtls.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.transport.internet.xtls.Certificate)
  })
_sym_db.RegisterMessage(Certificate)

Config = _reflection.GeneratedProtocolMessageType('Config', (_message.Message,), {
  'DESCRIPTOR' : _CONFIG,
  '__module__' : 'transport.internet.xtls.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.transport.internet.xtls.Config)
  })
_sym_db.RegisterMessage(Config)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n com.xray.transport.internet.xtlsP\001Z1github.com/xtls/xray-core/transport/internet/xtls\252\002\034Xray.Transport.Internet.Xtls'
  _CERTIFICATE._serialized_start=71
  _CERTIFICATE._serialized_end=345
  _CERTIFICATE_USAGE._serialized_start=277
  _CERTIFICATE_USAGE._serialized_end=345
  _CONFIG._serialized_start=348
  _CONFIG._serialized_end=728
# @@protoc_insertion_point(module_scope)
