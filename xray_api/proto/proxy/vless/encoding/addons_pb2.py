# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proxy/vless/encoding/addons.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n!proxy/vless/encoding/addons.proto\x12\x19xray.proxy.vless.encoding\"$\n\x06\x41\x64\x64ons\x12\x0c\n\x04\x46low\x18\x01 \x01(\t\x12\x0c\n\x04Seed\x18\x02 \x01(\x0c\x42m\n\x1d\x63om.xray.proxy.vless.encodingP\x01Z.github.com/xtls/xray-core/proxy/vless/encoding\xaa\x02\x19Xray.Proxy.Vless.Encodingb\x06proto3')



_ADDONS = DESCRIPTOR.message_types_by_name['Addons']
Addons = _reflection.GeneratedProtocolMessageType('Addons', (_message.Message,), {
  'DESCRIPTOR' : _ADDONS,
  '__module__' : 'proxy.vless.encoding.addons_pb2'
  # @@protoc_insertion_point(class_scope:xray.proxy.vless.encoding.Addons)
  })
_sym_db.RegisterMessage(Addons)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\035com.xray.proxy.vless.encodingP\001Z.github.com/xtls/xray-core/proxy/vless/encoding\252\002\031Xray.Proxy.Vless.Encoding'
  _ADDONS._serialized_start=64
  _ADDONS._serialized_end=100
# @@protoc_insertion_point(module_scope)
