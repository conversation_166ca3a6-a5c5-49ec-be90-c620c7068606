# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proxy/vmess/inbound/config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from xray_api.proto.common.protocol import user_pb2 as common_dot_protocol_dot_user__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n proxy/vmess/inbound/config.proto\x12\x18xray.proxy.vmess.inbound\x1a\x1a\x63ommon/protocol/user.proto\"\x1a\n\x0c\x44\x65tourConfig\x12\n\n\x02to\x18\x01 \x01(\t\"\x1e\n\rDefaultConfig\x12\r\n\x05level\x18\x02 \x01(\r\"\xa4\x01\n\x06\x43onfig\x12(\n\x04user\x18\x01 \x03(\x0b\x32\x1a.xray.common.protocol.User\x12\x38\n\x07\x64\x65\x66\x61ult\x18\x02 \x01(\x0b\x32\'.xray.proxy.vmess.inbound.DefaultConfig\x12\x36\n\x06\x64\x65tour\x18\x03 \x01(\x0b\x32&.xray.proxy.vmess.inbound.DetourConfigBj\n\x1c\x63om.xray.proxy.vmess.inboundP\x01Z-github.com/xtls/xray-core/proxy/vmess/inbound\xaa\x02\x18Xray.Proxy.Vmess.Inboundb\x06proto3')



_DETOURCONFIG = DESCRIPTOR.message_types_by_name['DetourConfig']
_DEFAULTCONFIG = DESCRIPTOR.message_types_by_name['DefaultConfig']
_CONFIG = DESCRIPTOR.message_types_by_name['Config']
DetourConfig = _reflection.GeneratedProtocolMessageType('DetourConfig', (_message.Message,), {
  'DESCRIPTOR' : _DETOURCONFIG,
  '__module__' : 'proxy.vmess.inbound.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.proxy.vmess.inbound.DetourConfig)
  })
_sym_db.RegisterMessage(DetourConfig)

DefaultConfig = _reflection.GeneratedProtocolMessageType('DefaultConfig', (_message.Message,), {
  'DESCRIPTOR' : _DEFAULTCONFIG,
  '__module__' : 'proxy.vmess.inbound.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.proxy.vmess.inbound.DefaultConfig)
  })
_sym_db.RegisterMessage(DefaultConfig)

Config = _reflection.GeneratedProtocolMessageType('Config', (_message.Message,), {
  'DESCRIPTOR' : _CONFIG,
  '__module__' : 'proxy.vmess.inbound.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.proxy.vmess.inbound.Config)
  })
_sym_db.RegisterMessage(Config)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\034com.xray.proxy.vmess.inboundP\001Z-github.com/xtls/xray-core/proxy/vmess/inbound\252\002\030Xray.Proxy.Vmess.Inbound'
  _DETOURCONFIG._serialized_start=90
  _DETOURCONFIG._serialized_end=116
  _DEFAULTCONFIG._serialized_start=118
  _DEFAULTCONFIG._serialized_end=148
  _CONFIG._serialized_start=151
  _CONFIG._serialized_end=315
# @@protoc_insertion_point(module_scope)
