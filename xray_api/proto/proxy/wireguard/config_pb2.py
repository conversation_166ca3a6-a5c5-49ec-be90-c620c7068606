# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proxy/wireguard/config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1cproxy/wireguard/config.proto\x12\x14xray.proxy.wireguard\"s\n\nPeerConfig\x12\x12\n\npublic_key\x18\x01 \x01(\t\x12\x16\n\x0epre_shared_key\x18\x02 \x01(\t\x12\x10\n\x08\x65ndpoint\x18\x03 \x01(\t\x12\x12\n\nkeep_alive\x18\x04 \x01(\r\x12\x13\n\x0b\x61llowed_ips\x18\x05 \x03(\t\"\xeb\x02\n\x0c\x44\x65viceConfig\x12\x12\n\nsecret_key\x18\x01 \x01(\t\x12\x10\n\x08\x65ndpoint\x18\x02 \x03(\t\x12/\n\x05peers\x18\x03 \x03(\x0b\x32 .xray.proxy.wireguard.PeerConfig\x12\x0b\n\x03mtu\x18\x04 \x01(\x05\x12\x13\n\x0bnum_workers\x18\x05 \x01(\x05\x12\x10\n\x08reserved\x18\x06 \x01(\x0c\x12J\n\x0f\x64omain_strategy\x18\x07 \x01(\x0e\x32\x31.xray.proxy.wireguard.DeviceConfig.DomainStrategy\x12\x11\n\tis_client\x18\x08 \x01(\x08\x12\x13\n\x0bkernel_mode\x18\t \x01(\x08\"\\\n\x0e\x44omainStrategy\x12\x0c\n\x08\x46ORCE_IP\x10\x00\x12\r\n\tFORCE_IP4\x10\x01\x12\r\n\tFORCE_IP6\x10\x02\x12\x0e\n\nFORCE_IP46\x10\x03\x12\x0e\n\nFORCE_IP64\x10\x04\x42^\n\x18\x63om.xray.proxy.wireguardP\x01Z)github.com/xtls/xray-core/proxy/wireguard\xaa\x02\x14Xray.Proxy.WireGuardb\x06proto3')



_PEERCONFIG = DESCRIPTOR.message_types_by_name['PeerConfig']
_DEVICECONFIG = DESCRIPTOR.message_types_by_name['DeviceConfig']
_DEVICECONFIG_DOMAINSTRATEGY = _DEVICECONFIG.enum_types_by_name['DomainStrategy']
PeerConfig = _reflection.GeneratedProtocolMessageType('PeerConfig', (_message.Message,), {
  'DESCRIPTOR' : _PEERCONFIG,
  '__module__' : 'proxy.wireguard.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.proxy.wireguard.PeerConfig)
  })
_sym_db.RegisterMessage(PeerConfig)

DeviceConfig = _reflection.GeneratedProtocolMessageType('DeviceConfig', (_message.Message,), {
  'DESCRIPTOR' : _DEVICECONFIG,
  '__module__' : 'proxy.wireguard.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.proxy.wireguard.DeviceConfig)
  })
_sym_db.RegisterMessage(DeviceConfig)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\030com.xray.proxy.wireguardP\001Z)github.com/xtls/xray-core/proxy/wireguard\252\002\024Xray.Proxy.WireGuard'
  _PEERCONFIG._serialized_start=54
  _PEERCONFIG._serialized_end=169
  _DEVICECONFIG._serialized_start=172
  _DEVICECONFIG._serialized_end=535
  _DEVICECONFIG_DOMAINSTRATEGY._serialized_start=443
  _DEVICECONFIG_DOMAINSTRATEGY._serialized_end=535
# @@protoc_insertion_point(module_scope)
