# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proxy/loopback/config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1bproxy/loopback/config.proto\x12\x13xray.proxy.loopback\"\x1d\n\x06\x43onfig\x12\x13\n\x0binbound_tag\x18\x01 \x01(\tB[\n\x17\x63om.xray.proxy.loopbackP\x01Z(github.com/xtls/xray-core/proxy/loopback\xaa\x02\x13Xray.Proxy.Loopbackb\x06proto3')



_CONFIG = DESCRIPTOR.message_types_by_name['Config']
Config = _reflection.GeneratedProtocolMessageType('Config', (_message.Message,), {
  'DESCRIPTOR' : _CONFIG,
  '__module__' : 'proxy.loopback.config_pb2'
  # @@protoc_insertion_point(class_scope:xray.proxy.loopback.Config)
  })
_sym_db.RegisterMessage(Config)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\027com.xray.proxy.loopbackP\001Z(github.com/xtls/xray-core/proxy/loopback\252\002\023Xray.Proxy.Loopback'
  _CONFIG._serialized_start=52
  _CONFIG._serialized_end=81
# @@protoc_insertion_point(module_scope)
