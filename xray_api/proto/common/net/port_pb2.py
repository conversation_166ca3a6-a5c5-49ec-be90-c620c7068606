# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: common/net/port.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15\x63ommon/net/port.proto\x12\x0fxray.common.net\"%\n\tPortRange\x12\x0c\n\x04\x46rom\x18\x01 \x01(\r\x12\n\n\x02To\x18\x02 \x01(\r\"5\n\x08PortList\x12)\n\x05range\x18\x01 \x03(\x0b\x32\x1a.xray.common.net.PortRangeBO\n\x13\x63om.xray.common.netP\x01Z$github.com/xtls/xray-core/common/net\xaa\x02\x0fXray.Common.Netb\x06proto3')



_PORTRANGE = DESCRIPTOR.message_types_by_name['PortRange']
_PORTLIST = DESCRIPTOR.message_types_by_name['PortList']
PortRange = _reflection.GeneratedProtocolMessageType('PortRange', (_message.Message,), {
  'DESCRIPTOR' : _PORTRANGE,
  '__module__' : 'common.net.port_pb2'
  # @@protoc_insertion_point(class_scope:xray.common.net.PortRange)
  })
_sym_db.RegisterMessage(PortRange)

PortList = _reflection.GeneratedProtocolMessageType('PortList', (_message.Message,), {
  'DESCRIPTOR' : _PORTLIST,
  '__module__' : 'common.net.port_pb2'
  # @@protoc_insertion_point(class_scope:xray.common.net.PortList)
  })
_sym_db.RegisterMessage(PortList)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\023com.xray.common.netP\001Z$github.com/xtls/xray-core/common/net\252\002\017Xray.Common.Net'
  _PORTRANGE._serialized_start=42
  _PORTRANGE._serialized_end=79
  _PORTLIST._serialized_start=81
  _PORTLIST._serialized_end=134
# @@protoc_insertion_point(module_scope)
