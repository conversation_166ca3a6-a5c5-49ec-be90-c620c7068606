# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: common/net/address.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x18\x63ommon/net/address.proto\x12\x0fxray.common.net\"7\n\nIPOrDomain\x12\x0c\n\x02ip\x18\x01 \x01(\x0cH\x00\x12\x10\n\x06\x64omain\x18\x02 \x01(\tH\x00\x42\t\n\x07\x61\x64\x64ressBO\n\x13\x63om.xray.common.netP\x01Z$github.com/xtls/xray-core/common/net\xaa\x02\x0fXray.Common.Netb\x06proto3')



_IPORDOMAIN = DESCRIPTOR.message_types_by_name['IPOrDomain']
IPOrDomain = _reflection.GeneratedProtocolMessageType('IPOrDomain', (_message.Message,), {
  'DESCRIPTOR' : _IPORDOMAIN,
  '__module__' : 'common.net.address_pb2'
  # @@protoc_insertion_point(class_scope:xray.common.net.IPOrDomain)
  })
_sym_db.RegisterMessage(IPOrDomain)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\023com.xray.common.netP\001Z$github.com/xtls/xray-core/common/net\252\002\017Xray.Common.Net'
  _IPORDOMAIN._serialized_start=45
  _IPORDOMAIN._serialized_end=100
# @@protoc_insertion_point(module_scope)
