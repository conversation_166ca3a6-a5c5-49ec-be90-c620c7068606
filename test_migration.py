#!/usr/bin/env python3
"""
Test script to verify that the migration imports work correctly
and that the models are properly defined.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_imports():
    """Test that all required imports work correctly."""
    try:
        # Test the enum import
        from app.models.resilient_node_group import ClientStrategyHint
        print("✓ ClientStrategyHint import successful")
        
        # Test that enum values are accessible
        print(f"✓ CLIENT_DEFAULT value: {ClientStrategyHint.CLIENT_DEFAULT}")
        print(f"✓ CLIENT_DEFAULT name: {ClientStrategyHint.CLIENT_DEFAULT.name}")
        
        # Test SQLAlchemy models import
        from app.db.models import ResilientNodeGroup, Node
        print("✓ SQLAlchemy models import successful")
        
        # Test that the association table is defined
        from app.db.models import resilient_node_group_nodes_association
        print("✓ Association table import successful")
        
        # Test Base metadata
        from app.db.base import Base
        print("✓ Base import successful")
        
        # Check if the tables are in metadata
        table_names = list(Base.metadata.tables.keys())
        print(f"✓ Tables in metadata: {table_names}")
        
        if 'resilient_node_groups' in table_names:
            print("✓ resilient_node_groups table found in metadata")
        else:
            print("✗ resilient_node_groups table NOT found in metadata")
            
        if 'resilient_node_group_nodes_association' in table_names:
            print("✓ resilient_node_group_nodes_association table found in metadata")
        else:
            print("✗ resilient_node_group_nodes_association table NOT found in metadata")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_migration_file():
    """Test that the migration file can be imported and executed."""
    try:
        # Import the migration module
        sys.path.insert(0, 'app/db/migrations/versions')
        import importlib.util
        
        spec = importlib.util.spec_from_file_location(
            "migration", 
            "app/db/migrations/versions/0a587bdb4f4f_add_load_balancer_related_tables.py"
        )
        migration_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(migration_module)
        
        print("✓ Migration file import successful")
        print(f"✓ Migration revision: {migration_module.revision}")
        print(f"✓ Migration down_revision: {migration_module.down_revision}")
        
        # Check if upgrade and downgrade functions exist
        if hasattr(migration_module, 'upgrade'):
            print("✓ upgrade() function found")
        else:
            print("✗ upgrade() function NOT found")
            
        if hasattr(migration_module, 'downgrade'):
            print("✓ downgrade() function found")
        else:
            print("✗ downgrade() function NOT found")
        
        return True
        
    except Exception as e:
        print(f"✗ Migration file error: {e}")
        return False

if __name__ == "__main__":
    print("Testing Resilient Node Groups Migration Setup...")
    print("=" * 50)
    
    imports_ok = test_imports()
    print()
    migration_ok = test_migration_file()
    
    print()
    print("=" * 50)
    if imports_ok and migration_ok:
        print("✓ All tests passed! Migration should work correctly.")
        sys.exit(0)
    else:
        print("✗ Some tests failed. Please check the errors above.")
        sys.exit(1)
